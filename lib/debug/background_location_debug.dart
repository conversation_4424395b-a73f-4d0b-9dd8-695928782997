import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:emartdriver/services/status_entregador_service.dart';
import 'package:emartdriver/ui/home/<USER>/localizacao_background_service.dart';
import 'package:emartdriver/ui/home/<USER>/notificacao_foreground_service.dart';

class BackgroundLocationDebugScreen extends StatefulWidget {
  const BackgroundLocationDebugScreen({Key? key}) : super(key: key);

  @override
  State<BackgroundLocationDebugScreen> createState() =>
      _BackgroundLocationDebugScreenState();
}

class _BackgroundLocationDebugScreenState
    extends State<BackgroundLocationDebugScreen> {
  final StatusEntregadorService _statusService = StatusEntregadorService();
  final LocalizacaoBackgroundService _localizacaoService =
      LocalizacaoBackgroundService();
  final NotificacaoForegroundService _notificacaoService =
      NotificacaoForegroundService();

  bool _isOnline = false;
  LatLng? _currentPosition;
  String _debugLog = '';
  Timer? _debugTimer;

  @override
  void initState() {
    super.initState();
    _initializeDebug();
  }

  @override
  void dispose() {
    _debugTimer?.cancel();
    super.dispose();
  }

  void _initializeDebug() {
    _statusService.addListener(_onStatusChanged);
    _localizacaoService.addListener(_onLocationChanged);
    _notificacaoService.addListener(_onNotificationChanged);

    // Timer para log periódico
    _debugTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _logDebugInfo();
    });

    _logDebugInfo();
  }

  void _onStatusChanged(bool isOnline) {
    setState(() {
      _isOnline = isOnline;
    });
    _addLog('Status changed: ${isOnline ? 'ONLINE' : 'OFFLINE'}');
  }

  void _onLocationChanged() {
    setState(() {
      _currentPosition = _localizacaoService.posicaoAtual;
    });
    if (_currentPosition != null) {
      _addLog(
          'Location updated: ${_currentPosition!.latitude.toStringAsFixed(6)}, ${_currentPosition!.longitude.toStringAsFixed(6)}');
    }
  }

  void _onNotificationChanged() {
    _addLog(
        'Notification service: ${_notificacaoService.servicoAtivo ? 'ACTIVE' : 'INACTIVE'}');
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toIso8601String();
    setState(() {
      _debugLog = '[$timestamp] $message\n$_debugLog';
    });
    developer.log(message, name: 'BackgroundLocationDebug');
  }

  void _logDebugInfo() async {
    // Verificar permissões
    final permission = await Geolocator.checkPermission();
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();

    _addLog('=== DEBUG INFO ===');
    _addLog('Location permission: $permission');
    _addLog('Location service enabled: $serviceEnabled');
    _addLog('Status service online: $_isOnline');
    _addLog('Location service active: ${_localizacaoService.servicoAtivo}');
    _addLog('Notification service active: ${_notificacaoService.servicoAtivo}');
    _addLog('Current position: $_currentPosition');
    _addLog('==================');
  }

  Future<void> _startBackgroundLocation() async {
    try {
      _addLog('Starting background location...');
      await _statusService.setOnlineStatus(true);
      _addLog('Background location started successfully');
    } catch (e) {
      _addLog('Error starting background location: $e');
    }
  }

  Future<void> _stopBackgroundLocation() async {
    try {
      _addLog('Stopping background location...');
      await _statusService.setOnlineStatus(false);
      _addLog('Background location stopped successfully');
    } catch (e) {
      _addLog('Error stopping background location: $e');
    }
  }

  Future<void> _testPermissions() async {
    try {
      _addLog('Testing permissions...');
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      _addLog('Location service enabled: $serviceEnabled');

      if (!serviceEnabled) {
        _addLog('Location services are disabled');
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      _addLog('Current permission: $permission');

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        _addLog('Requested permission result: $permission');
      }

      if (permission == LocationPermission.deniedForever) {
        _addLog('Location permissions are permanently denied');
        return;
      }

      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        _addLog('Location permissions granted');
        
        // Tentar obter posição atual
        final position = await Geolocator.getCurrentPosition(
          locationSettings: const LocationSettings(
            accuracy: LocationAccuracy.high,
          ),
        );
        _addLog('Current position: ${position.latitude}, ${position.longitude}');
      }
    } catch (e) {
      _addLog('Error testing permissions: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Background Location Debug'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Status Cards
            Row(
              children: [
                Expanded(
                  child: Card(
                    color: _isOnline ? Colors.green[100] : Colors.red[100],
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Icon(
                            _isOnline ? Icons.online_prediction : Icons.offline_bolt,
                            color: _isOnline ? Colors.green : Colors.red,
                          ),
                          Text(_isOnline ? 'ONLINE' : 'OFFLINE'),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Card(
                    color: _localizacaoService.servicoAtivo
                        ? Colors.green[100]
                        : Colors.red[100],
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Icon(
                            _localizacaoService.servicoAtivo
                                ? Icons.location_on
                                : Icons.location_off,
                            color: _localizacaoService.servicoAtivo
                                ? Colors.green
                                : Colors.red,
                          ),
                          Text(_localizacaoService.servicoAtivo
                              ? 'TRACKING'
                              : 'NOT TRACKING'),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            // Position Info
            if (_currentPosition != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      const Text('Current Position:'),
                      Text(
                          'Lat: ${_currentPosition!.latitude.toStringAsFixed(6)}'),
                      Text(
                          'Lng: ${_currentPosition!.longitude.toStringAsFixed(6)}'),
                    ],
                  ),
                ),
              ),

            // Control Buttons
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _startBackgroundLocation,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                    child: const Text('Start Tracking'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _stopBackgroundLocation,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: const Text('Stop Tracking'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _testPermissions,
              child: const Text('Test Permissions'),
            ),

            // Debug Log
            const SizedBox(height: 16),
            const Text('Debug Log:', style: TextStyle(fontWeight: FontWeight.bold)),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugLog.isEmpty ? 'No logs yet...' : _debugLog,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
