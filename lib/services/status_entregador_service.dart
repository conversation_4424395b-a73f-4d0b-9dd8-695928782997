import 'dart:async';

import 'package:background_locator_2/background_locator.dart';
import 'package:background_locator_2/location_dto.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/ui/home/<USER>/localizacao_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();
  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  bool _isOnline = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  final LocalizacaoService _localizacaoService = LocalizacaoService();
  LatLng? _ultimaPosicaoSalva;
  StreamSubscription? _monitoramentoPosicao;

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno();

  bool get isOnline => _isOnline;

  Future<void> initialize() async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;
    _statusSubscription?.cancel();
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      _isOnline = online;
      _notificarListeners(online);
    });
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    _isOnline = dados?['isOnline'] == true;

    if (_isOnline) {
      _iniciarMonitoramentoLocalizacao();
    }
  }

  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
    _monitoramentoPosicao?.cancel();
    _localizacaoService.dispose();
  }

  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude}) async {
    final String id = MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) return;
    final Map<String, dynamic> dados = {
      'entregador_id': id,
      'lastActive': Timestamp.now(),
      'isOnline': online,
      'updatedAt': Timestamp.now(),
    };
    if (latitude != null && longitude != null) {
      dados['location'] = {
        'latitude': latitude,
        'longitude': longitude,
      };
      _ultimaPosicaoSalva = LatLng(latitude, longitude);
    }
    await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));
    _isOnline = online;
    _notificarListeners(online);

    if (online) {
      _iniciarMonitoramentoLocalizacao();
      await iniciarBackgroundLocation();
    } else {
      _pararMonitoramentoLocalizacao();
      await pararBackgroundLocation();
    }
  }

  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }
    final doc = await _colecaoStatus.doc(id).get();
    return doc.data() as Map<String, dynamic>?;
  }

  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      listener(online);
    }
  }

  void _iniciarMonitoramentoLocalizacao() {
    _monitoramentoPosicao?.cancel();
    _localizacaoService.iniciarMonitoramentoPosicao((LatLng novaPosicao) async {
      if (_ultimaPosicaoSalva == null) {
        _ultimaPosicaoSalva = novaPosicao;
        await setOnlineStatus(true,
            latitude: novaPosicao.latitude, longitude: novaPosicao.longitude);
        return;
      }
      final double distancia = Geolocator.distanceBetween(
        _ultimaPosicaoSalva!.latitude,
        _ultimaPosicaoSalva!.longitude,
        novaPosicao.latitude,
        novaPosicao.longitude,
      );
      if (distancia > 20) {
        await setOnlineStatus(true,
            latitude: novaPosicao.latitude, longitude: novaPosicao.longitude);
      }
    });
  }

  void _pararMonitoramentoLocalizacao() {
    _localizacaoService.pararMonitoramentoPosicao();
    _monitoramentoPosicao?.cancel();
    _monitoramentoPosicao = null;
  }

  Future<void> iniciarBackgroundLocation() async {
    try {
      await BackgroundLocator.initialize();
      await BackgroundLocator.registerLocationUpdate(
        backgroundCallback,
        initCallback: initCallback,
        disposeCallback: notificationCallback,
        autoStop: false,
      );
    } catch (e) {
      print('Erro ao iniciar background location: $e');
    }
  }

  Future<void> pararBackgroundLocation() async {
    await BackgroundLocator.unRegisterLocationUpdate();
  }
}

void backgroundCallback(LocationDto locationDto) async {
  // Envia a localização para o servidor
  try {
    await StatusEntregadorService().setOnlineStatus(
      true,
      latitude: locationDto.latitude,
      longitude: locationDto.longitude,
    );
  } catch (e) {
    // Log do erro sem usar print em produção
  }
}

void initCallback(Map<dynamic, dynamic> params) {
  // Callback de inicialização do background locator
}

void notificationCallback() {
  // Callback de notificação do background locator
}
