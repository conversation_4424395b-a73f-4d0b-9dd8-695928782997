import 'package:flutter/material.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/vendor_status_enum.dart';

class BottomsheetDevolucaoRetorno extends StatelessWidget {
  final OrderModel pedidoDevolucao;
  final VoidCallback aoConfirmarDevolucao;

  const BottomsheetDevolucaoRetorno({
    super.key,
    required this.pedidoDevolucao,
    required this.aoConfirmarDevolucao,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.40,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildIndicadorSuperior(),
          const SizedBox(height: 20),
          _buildTitulo(),
          const SizedBox(height: 15),
          _buildInstrucaoRetorno(),
          const SizedBox(height: 10),
          _buildInformacaoLoja(),
          const SizedBox(height: 20),
          _buildBotaoConfirmar(context),
        ],
      ),
    );
  }

  Widget _buildIndicadorSuperior() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 20),
        GestureDetector(
          onTap: () {},
          child: Container(
            width: 50,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitulo() {
    return Text(
      'Entrega com Retorno - ${pedidoDevolucao.vendor.title}',
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xff425799),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildInstrucaoRetorno() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Color.fromARGB(39, 201, 212, 247),
      ),
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset('assets/images/caixa.png', height: 30, width: 30),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              "Retorne ao estabelecimento para finalizar a entrega",
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInformacaoLoja() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Color.fromARGB(39, 247, 201, 201),
      ),
      width: double.infinity,
      child: Column(
        children: [
          Text(
            "Loja: ${pedidoDevolucao.vendor.title}",
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 5),
          Text(
            _obterEnderecoCompleto(),
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBotaoConfirmar(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () => _aoConfirmarDevolucao(context),
            child: const Text(
              "Confirmar Devolução",
              style: TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  String _obterEnderecoCompleto() {
    final endereco = pedidoDevolucao.vendor.address_store;
    return "${endereco?.logradouro ?? ""}, ${endereco?.numero ?? ""}, ${endereco?.bairro ?? ""}, ${endereco?.cidade ?? ""}";
  }

  Future<void> _aoConfirmarDevolucao(BuildContext context) async {
    Navigator.pop(context);

    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(pedidoDevolucao.id);

      await showProgress(context, 'Confirmando Devolução', false);

      await docRef.update({
        'status': OrderStatus.returned.description,
      });

      aoConfirmarDevolucao();

      await Future.delayed(const Duration(seconds: 2));
      await hideProgress();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Devolução confirmada com sucesso!"),
          ),
        );
      }
    } catch (e) {
      hideProgress();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erro ao confirmar devolução: $e")),
        );
      }
    }
  }
}
