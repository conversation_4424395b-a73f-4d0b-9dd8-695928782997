import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeState extends ChangeNotifier {
  LatLng? _posicaoAtual;
  bool _carregando = true;
  bool _entregadorOnline = false;
  OrderModel? _pedidoSelecionado;
  RouteInfo? _informacaoRota;
  bool _pedidoAceito = false;
  bool _exibindoNotificacaoNovoPedido = false;
  bool _bottomsheetDevolucaoAtivo = false;
  BitmapDescriptor? _iconeEntregador;

  // Getters
  LatLng? get posicaoAtual => _posicaoAtual;
  bool get carregando => _carregando;
  bool get entregadorOnline => _entregadorOnline;
  OrderModel? get pedidoSelecionado => _pedidoSelecionado;
  RouteInfo? get informacaoRota => _informacaoRota;
  bool get pedidoAceito => _pedidoAceito;
  bool get exibindoNotificacaoNovoPedido => _exibindoNotificacaoNovoPedido;
  bool get bottomsheetDevolucaoAtivo => _bottomsheetDevolucaoAtivo;
  BitmapDescriptor? get iconeEntregador => _iconeEntregador;

  void atualizarPosicaoAtual(LatLng posicao) {
    _posicaoAtual = posicao;
    notifyListeners();
  }

  void definirCarregamento(bool carregando) {
    _carregando = carregando;
    notifyListeners();
  }

  void atualizarStatusOnline(bool online) {
    _entregadorOnline = online;
    notifyListeners();
  }

  void selecionarPedido(OrderModel? pedido) {
    _pedidoSelecionado = pedido;
    notifyListeners();
  }

  void definirInformacaoRota(RouteInfo? informacao) {
    _informacaoRota = informacao;
    notifyListeners();
  }

  void alterarStatusPedidoAceito(bool aceito) {
    _pedidoAceito = aceito;
    notifyListeners();
  }

  void atualizarNotificacaoNovoPedido(bool exibindo) {
    _exibindoNotificacaoNovoPedido = exibindo;
    notifyListeners();
  }

  void alterarBottomsheetDevolucao(bool ativo) {
    _bottomsheetDevolucaoAtivo = ativo;
    notifyListeners();
  }

  void definirIconeEntregador(BitmapDescriptor icone) {
    _iconeEntregador = icone;
    notifyListeners();
  }

  void limparSelecao() {
    _pedidoSelecionado = null;
    _informacaoRota = null;
    _pedidoAceito = false;
    notifyListeners();
  }
}
