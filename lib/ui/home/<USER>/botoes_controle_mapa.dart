import 'package:flutter/material.dart';

class BotoesControleMapa extends StatelessWidget {
  final VoidCallback aoZoomIn;
  final VoidCallback aoZoomOut;
  final VoidCallback aoCentralizarLocalizacao;
  final VoidCallback aoRecarregarMapa;

  const BotoesControleMapa({
    super.key,
    required this.aoZoomIn,
    required this.aoZoomOut,
    required this.aoCentralizarLocalizacao,
    required this.aoRecarregarMapa,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 5,
      top: 6,
      child: Column(
        children: [
          _buildBotaoControle(
            heroTag: 'zoomInButton',
            icone: Icons.zoom_in_outlined,
            aoClicar: aoZoomIn,
          ),
          const SizedBox(height: 16),
          _buildBotaoControle(
            heroTag: "zoomOutButton",
            icone: Icons.zoom_out_outlined,
            aoClicar: aoZoomOut,
          ),
          const Sized<PERSON>ox(height: 16),
          _buildBotaoControle(
            heroTag: "centerLocationButton",
            icone: Icons.my_location,
            aoClicar: aoCentralizarLocalizacao,
          ),
          const SizedBox(height: 16),
          _buildBotaoControle(
            heroTag: "reloadMapButton",
            icone: Icons.refresh,
            aoClicar: aoRecarregarMapa,
          ),
        ],
      ),
    );
  }

  Widget _buildBotaoControle({
    required String heroTag,
    required IconData icone,
    required VoidCallback aoClicar,
  }) {
    return FloatingActionButton(
      heroTag: heroTag,
      mini: false,
      backgroundColor: Colors.white,
      onPressed: aoClicar,
      child: Icon(
        icone,
        color: const Color(0xff425799),
      ),
    );
  }
}
