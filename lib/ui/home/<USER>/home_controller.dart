import 'dart:async';
import 'dart:developer';

import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/OrderMonitor.dart';
import 'package:emartdriver/ui/home/<USER>/gerenciador_marcadores.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/firebase_order_service.dart';
import 'package:emartdriver/ui/home/<USER>/icone_service.dart';
import 'package:emartdriver/ui/home/<USER>/localizacao_service.dart';
import 'package:emartdriver/ui/home/<USER>/monitoramento_pedidos_service.dart';
import 'package:emartdriver/ui/home/<USER>/processador_pedidos_service.dart';
import 'package:emartdriver/ui/home/<USER>/home_state.dart';
import 'package:emartdriver/ui/home/<USER>/mapa_state.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeController extends ChangeNotifier {
  final HomeState _homeState = HomeState();
  final MapaState _mapaState = MapaState();

  final FirebaseOrderService _firebaseService = FirebaseOrderService();
  final LocalizacaoService _localizacaoService = LocalizacaoService();
  final MonitoramentoPedidosService _monitoramentoService =
      MonitoramentoPedidosService();
  final IconeService _iconeService = IconeService();
  final ProcessadorPedidosService _processadorService =
      ProcessadorPedidosService();
  final OrderMonitor _orderMonitor = OrderMonitor();

  final String _entregadorId = FirebaseAuth.instance.currentUser?.uid ?? '';

  // Getters para os states
  HomeState get homeState => _homeState;
  MapaState get mapaState => _mapaState;
  String get entregadorId => _entregadorId;

  Future<void> inicializar() async {
    await _carregarIconeEntregador();
    await _obterPosicaoInicial();
    await _iniciarServicosBackground();
  }

  Future<void> _carregarIconeEntregador() async {
    final icone = await _iconeService.carregarIconeEntregador();
    _homeState.definirIconeEntregador(icone);
  }

  Future<void> _obterPosicaoInicial() async {
    final posicao = await _localizacaoService.obterPosicaoAtual();

    if (posicao == null) {
      _homeState.definirCarregamento(false);
      return;
    }

    _homeState.atualizarPosicaoAtual(posicao);
    _homeState.definirCarregamento(false);
    _atualizarMarcadorEntregador();
    _iniciarMonitoramentoPosicao();
    _iniciarListenersFirebase();
    _inicializarMonitorPedidos();
  }

  void _iniciarMonitoramentoPosicao() {
    _localizacaoService.iniciarMonitoramentoPosicao((posicao) {
      _homeState.atualizarPosicaoAtual(posicao);
      _atualizarMarcadorEntregador();
    });
  }

  void _atualizarMarcadorEntregador() {
    final posicao = _homeState.posicaoAtual;
    final icone = _homeState.iconeEntregador;

    if (posicao == null || icone == null) return;

    final marcador = GerenciadorMarcadores.criarMarcadorEntregador(
      posicao: posicao,
      icone: icone,
    );

    _mapaState.adicionarMarcador('entregador', marcador);
  }

  void _iniciarListenersFirebase() {
    _verificarPedidoAceito();
  }

  Future<void> _verificarPedidoAceito() async {
    try {
      final snapshotDevolucao =
          await _firebaseService.buscarPedidosDevolucao(_entregadorId);

      if (snapshotDevolucao.docs.isNotEmpty) {
        final pedidoDevolucao = OrderModel.fromJson(
            snapshotDevolucao.docs.first.data() as Map<String, dynamic>);
        _processarPedidoDevolucao(pedidoDevolucao);
        return;
      }

      final snapshotAceito =
          await _firebaseService.buscarPedidosAceitos(_entregadorId);

      if (snapshotAceito.docs.isNotEmpty) {
        final pedidoId = snapshotAceito.docs.first.id;
        _escutarPedidoAceito(pedidoId);
      } else {
        _escutarPedidosDisponiveis();
      }
    } catch (e) {
      log("Erro ao verificar pedidos aceitos: $e");
    }
  }

  void _escutarPedidoAceito(String pedidoId) {
    _monitoramentoService.iniciarMonitoramentoPedidoAceito(
      pedidoId: pedidoId,
      aoAtualizarPedido: _processarAtualizacaoPedidoAceito,
      aoPedidoNaoEncontrado: _escutarPedidosDisponiveis,
    );
  }

  void _escutarPedidosDisponiveis() {
    if (!_homeState.entregadorOnline) {
      _mapaState.preservarMarcadorEntregador();
      return;
    }

    _monitoramentoService.iniciarMonitoramentoPedidosDisponiveis(
      aoReceberPedidos: _processarPedidosDisponiveis,
    );
  }

  void _processarPedidosDisponiveis(List<OrderModel> pedidos) {
    final posicaoEntregador = _homeState.posicaoAtual;

    if (posicaoEntregador == null || _homeState.pedidoAceito) {
      return;
    }

    _mapaState.preservarMarcadorEntregador();

    final pedidosPorLoja = _processadorService.agruparPedidosPorLoja(
      pedidos,
      posicaoEntregador,
    );

    _criarMarcadoresLojas(pedidosPorLoja);
  }

  void _criarMarcadoresLojas(Map<String, List<OrderModel>> pedidosPorLoja) {
    pedidosPorLoja.forEach((lojaId, pedidos) {
      if (pedidos.isEmpty) return;

      final primeiroPedido = pedidos.first;
      final posicaoLoja =
          GerenciadorMarcadores.extrairPosicaoLoja(primeiroPedido);

      if (posicaoLoja == null) return;

      final titulo = GerenciadorMarcadores.obterTituloMarcadorLoja(
        primeiroPedido.vendor.title,
        pedidos.length,
      );

      final marcador = GerenciadorMarcadores.criarMarcadorLoja(
        lojaId: lojaId,
        posicao: posicaoLoja,
        titulo: titulo,
        multiplosPedidos: pedidos.length > 1,
        aoClicar: () => _selecionarPedido(pedidos),
      );

      _mapaState.adicionarMarcador('loja_$lojaId', marcador);
    });
  }

  void _selecionarPedido(List<OrderModel> pedidos) {
    if (pedidos.length == 1) {
      _selecionarPedidoUnico(pedidos.first);
    } else {
      // Aqui seria chamado o seletor de múltiplos pedidos
      // GerenciadorModaisPedidos.exibirSeletorMultiplosPedidos(...)
    }
  }

  Future<void> _selecionarPedidoUnico(OrderModel pedido) async {
    final posicaoEntregador = _homeState.posicaoAtual;
    final posicaoLoja = GerenciadorMarcadores.extrairPosicaoLoja(pedido);
    final posicaoCliente = GerenciadorMarcadores.extrairPosicaoCliente(pedido);

    if (posicaoEntregador == null ||
        posicaoLoja == null ||
        posicaoCliente == null) {
      return;
    }

    _homeState.selecionarPedido(pedido);
    _homeState.alterarStatusPedidoAceito(false);
    _mapaState.limparPolylines();
    _mapaState.preservarMarcadorEntregador();

    await _criarRotaParaPedido(
        pedido, posicaoEntregador, posicaoLoja, posicaoCliente);
  }

  Future<void> _criarRotaParaPedido(
    OrderModel pedido,
    LatLng posicaoEntregador,
    LatLng posicaoLoja,
    LatLng posicaoCliente,
  ) async {
    final informacaoRota = await getRouteInfo(posicaoLoja, posicaoCliente);
    final rotaParaLoja =
        await getRouteCoordinates(posicaoEntregador, posicaoLoja);

    _homeState.definirInformacaoRota(RouteInfo(
      route: [],
      distance: informacaoRota.distance,
      duration: informacaoRota.duration,
    ));

    final polyline = GerenciadorMarcadores.criarPolyline(
      id: "rota_para_loja",
      pontos: rotaParaLoja,
      cor: Colors.orange,
    );

    _mapaState.adicionarPolyline(polyline);
    _criarMarcadorDestino(pedido);
  }

  void _criarMarcadorDestino(OrderModel pedido) {
    final eEntrega = pedido.status == OrderStatus.driverOnTheWay.description;

    final posicaoDestino = eEntrega
        ? GerenciadorMarcadores.extrairPosicaoCliente(pedido)
        : GerenciadorMarcadores.extrairPosicaoLoja(pedido);

    if (posicaoDestino == null) return;

    final titulo = eEntrega
        ? "Entrega para: ${pedido.author.firstName} ${pedido.author.lastName}"
        : "Loja: ${pedido.vendor.title}";

    final marcador = GerenciadorMarcadores.criarMarcadorDestino(
      posicao: posicaoDestino,
      titulo: titulo,
      snippet: "",
      eEntrega: eEntrega,
      aoClicar: () {
        // Aqui seria chamado o diálogo apropriado
      },
    );

    _mapaState.adicionarMarcador('destino', marcador);
  }

  void _processarAtualizacaoPedidoAceito(OrderModel pedido) {
    // Lógica para processar atualizações do pedido aceito
    _homeState.selecionarPedido(pedido);
    _homeState.alterarStatusPedidoAceito(true);

    // Criar rotas e marcadores para o pedido aceito
    _criarVisualizacaoPedidoAceito(pedido);
  }

  Future<void> _criarVisualizacaoPedidoAceito(OrderModel pedido) async {
    final posicaoEntregador = _homeState.posicaoAtual;
    if (posicaoEntregador == null) return;

    final eEntrega = pedido.status == OrderStatus.driverOnTheWay.description;

    final posicaoDestino = eEntrega
        ? GerenciadorMarcadores.extrairPosicaoCliente(pedido)
        : GerenciadorMarcadores.extrairPosicaoLoja(pedido);

    if (posicaoDestino == null) return;

    final rota = await getRouteCoordinates(posicaoEntregador, posicaoDestino);

    _mapaState.limparPolylines();
    _mapaState.preservarMarcadorEntregador();

    final polyline = GerenciadorMarcadores.criarPolyline(
      id: eEntrega ? "rota_ate_cliente" : "rota_ate_loja",
      pontos: rota,
      cor: Colors.orange,
    );

    _mapaState.adicionarPolyline(polyline);
    _criarMarcadorDestino(pedido);
  }

  void _processarPedidoDevolucao(OrderModel pedidoDevolucao) {
    _homeState.selecionarPedido(pedidoDevolucao);
    _homeState.alterarStatusPedidoAceito(true);
    _criarVisualizacaoDevolucao(pedidoDevolucao);
  }

  Future<void> _criarVisualizacaoDevolucao(OrderModel pedidoDevolucao) async {
    final posicaoEntregador = _homeState.posicaoAtual;
    final posicaoLoja =
        GerenciadorMarcadores.extrairPosicaoLoja(pedidoDevolucao);

    if (posicaoEntregador == null || posicaoLoja == null) return;

    final rotaDevolucao =
        await getRouteCoordinates(posicaoEntregador, posicaoLoja);

    _mapaState.limparPolylines();
    _mapaState.preservarMarcadorEntregador();

    final polyline = GerenciadorMarcadores.criarPolyline(
      id: "rota_de_retorno",
      pontos: rotaDevolucao,
      cor: Colors.red,
    );

    _mapaState.adicionarPolyline(polyline);

    final marcador = GerenciadorMarcadores.criarMarcadorDevolucao(
      posicao: posicaoLoja,
      titulo: "Devolução para: ${pedidoDevolucao.vendor.title}",
      aoClicar: () {
        // Aqui seria chamado o bottomsheet de devolução
      },
    );

    _mapaState.adicionarMarcador('destino', marcador);
  }

  void _inicializarMonitorPedidos() {
    final posicao = _homeState.posicaoAtual;
    if (posicao != null && _homeState.entregadorOnline) {
      _orderMonitor.initialize(
        onNewOrder: _processarNovoPedido,
        currentPosition: posicao,
        currentUserId: _entregadorId,
      );
    }
  }

  void _processarNovoPedido(OrderModel pedido) {
    // Lógica para processar novo pedido
    if (!_homeState.pedidoAceito && !_homeState.exibindoNotificacaoNovoPedido) {
      _homeState.atualizarNotificacaoNovoPedido(true);
      // Aqui seria exibido o bottomsheet do novo pedido
    }
  }

  Future<void> aceitarPedido(OrderModel pedido) async {
    try {
      await _firebaseService.aceitarPedido(pedido.id, _entregadorId);

      _homeState.selecionarPedido(pedido);
      _homeState.alterarStatusPedidoAceito(true);

      _escutarPedidoAceito(pedido.id);
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      rethrow;
    }
  }

  void aoMudarStatusOnline(bool online) {
    _homeState.atualizarStatusOnline(online);

    if (online) {
      _inicializarMonitorPedidos();
      _iniciarListenersFirebase();
    } else {
      _orderMonitor.dispose();
      _monitoramentoService.pararTodosMonitoramentos();

      if (!_homeState.pedidoAceito) {
        _mapaState.preservarMarcadorEntregador();
      }
    }
  }

  void aoMapaCriado(GoogleMapController controlador) {
    _mapaState.definirControladorMapa(controlador);

    final posicao = _homeState.posicaoAtual;
    if (posicao != null) {
      controlador.animateCamera(
        CameraUpdate.newLatLngZoom(posicao, 15),
      );
      _atualizarMarcadorEntregador();
    }
  }

  void aoClicarMapa(LatLng posicao) {
    if (_homeState.pedidoSelecionado != null && !_homeState.pedidoAceito) {
      _homeState.limparSelecao();
      _mapaState.limparPolylines();
      _mapaState.preservarMarcadorEntregador();
      // Buscar pedidos novamente
      _escutarPedidosDisponiveis();
    }
  }

  void centralizarNoEntregador() {
    final controlador = _mapaState.controladorMapa;
    final posicao = _homeState.posicaoAtual;

    if (controlador == null || posicao == null) return;

    controlador.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: posicao, zoom: 17.0, tilt: 0),
      ),
    );
  }

  void aumentarZoom() {
    final controlador = _mapaState.controladorMapa;
    if (controlador == null) return;

    controlador.getZoomLevel().then((zoomAtual) {
      final novoZoom = (zoomAtual + 1.0).clamp(2.0, 20.0);
      controlador.animateCamera(CameraUpdate.zoomTo(novoZoom));
    });
  }

  void diminuirZoom() {
    final controlador = _mapaState.controladorMapa;
    if (controlador == null) return;

    controlador.getZoomLevel().then((zoomAtual) {
      final novoZoom = (zoomAtual - 1.0).clamp(2.0, 20.0);
      controlador.animateCamera(CameraUpdate.zoomTo(novoZoom));
    });
  }

  void recarregarMapa() {
    _mapaState.limparMarcadores();
    _mapaState.limparPolylines();
    _obterPosicaoInicial();
    _escutarPedidosDisponiveis();

    if (_homeState.posicaoAtual != null) {
      _atualizarMarcadorEntregador();
    }
  }

  Future<void> _iniciarServicosBackground() async {
    final sucesso = await _localizacaoService.iniciarMonitoramentoBackground();
    if (sucesso) {
      _configurarListenerBackground();
    }
  }

  void _configurarListenerBackground() {
    _localizacaoService.configurarCallbackBackground((novaPosicao) {
      _homeState.atualizarPosicaoAtual(novaPosicao);
      _atualizarMarcadorEntregador();
    });
  }

  @override
  void dispose() {
    _localizacaoService.dispose();
    _monitoramentoService.dispose();
    _orderMonitor.dispose();
    super.dispose();
  }
}
