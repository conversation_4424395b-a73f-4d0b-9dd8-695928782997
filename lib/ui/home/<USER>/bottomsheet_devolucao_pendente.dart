import 'package:flutter/material.dart';

class BottomsheetDevolucaoPendente extends StatelessWidget {
  const BottomsheetDevolucaoPendente({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.50,
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildIndicadorSuperior(),
          const SizedBox(height: 24),
          _buildIconeAviso(),
          const SizedBox(height: 16),
          _buildTitulo(),
          const SizedBox(height: 16),
          _buildConteudoInformativo(),
          const Spacer(),
          _buildBotaoAguardar(context),
        ],
      ),
    );
  }

  Widget _buildIndicadorSuperior() {
    return Container(
      width: 60,
      height: 6,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(10),
      ),
    );
  }

  Widget _buildIconeAviso() {
    return Icon(
      Icons.warning_rounded,
      size: 64,
      color: Colors.orange[600],
    );
  }

  Widget _buildTitulo() {
    return const Text(
      'Devolução Pendente',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xff425799),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildConteudoInformativo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        children: [
          _buildCabecalhoInformacao(),
          const SizedBox(height: 12),
          _buildTextoExplicativo(),
        ],
      ),
    );
  }

  Widget _buildCabecalhoInformacao() {
    return Row(
      children: [
        Icon(
          Icons.info_outline,
          color: Colors.orange[700],
          size: 20,
        ),
        const SizedBox(width: 8),
        const Expanded(
          child: Text(
            "Você possui uma entrega com devolução pendente",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xff425799),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextoExplicativo() {
    return const Text(
      "Você não poderá aceitar novos pedidos até que o lojista confirme o recebimento do produto devolvido. Por favor, aguarde a confirmação.",
      style: TextStyle(
        fontSize: 14,
        color: Colors.black87,
        height: 1.4,
      ),
      textAlign: TextAlign.left,
    );
  }

  Widget _buildBotaoAguardar(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: () => _aoClicarAguardar(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xff425799),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: const Text(
          'Aguardando Confirmação',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _aoClicarAguardar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Aguarde a confirmação do lojista para continuar'),
        backgroundColor: Color(0xff425799),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
