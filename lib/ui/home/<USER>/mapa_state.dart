import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapaState extends ChangeNotifier {
  GoogleMapController? _controladorMapa;
  final Map<String, Marker> _marcadores = {};
  final Set<Polyline> _polylines = {};

  GoogleMapController? get controladorMapa => _controladorMapa;
  Map<String, Marker> get marcadores => Map.unmodifiable(_marcadores);
  Set<Polyline> get polylines => Set.unmodifiable(_polylines);

  void definirControladorMapa(GoogleMapController controlador) {
    _controladorMapa = controlador;
    notifyListeners();
  }

  void adicionarMarcador(String chave, Marker marcador) {
    _marcadores[chave] = marcador;
    notifyListeners();
  }

  void removerMarcador(String chave) {
    _marcadores.remove(chave);
    notifyListeners();
  }

  void limparMarcadores() {
    _marcadores.clear();
    notifyListeners();
  }

  void preservarMarcadorEntregador() {
    final marcadorEntregador = _marcadores['entregador'];
    _marcadores.clear();
    if (marcadorEntregador != null) {
      _marcadores['entregador'] = marcadorEntregador;
    }
    notifyListeners();
  }

  void adicionarPolyline(Polyline polyline) {
    _polylines.add(polyline);
    notifyListeners();
  }

  void limparPolylines() {
    _polylines.clear();
    notifyListeners();
  }

  void atualizarVisualizacao() {
    notifyListeners();
  }
}
