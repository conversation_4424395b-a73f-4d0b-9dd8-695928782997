class PermissoesAndroid {
  static const List<String> permissoesNecessarias = [
    'android.permission.ACCESS_FINE_LOCATION',
    'android.permission.ACCESS_COARSE_LOCATION',
    'android.permission.ACCESS_BACKGROUND_LOCATION',
    'android.permission.FOREGROUND_SERVICE',
  ];

  static const String manifestContent = '''
<!-- Permissões de localização para funcionamento em foreground -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

<!-- Permissão para localização em background (Android 10+) -->
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>

<!-- Permissão para serviços em foreground -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

<!-- Configuração do serviço em foreground no application -->
<application>
    <service 
        android:name=".LocalizacaoForegroundService"
        android:enabled="true"
        android:exported="false" 
        android:foregroundServiceType="location" />
</application>
''';

  static String obterTextoPermissoes() {
    return '''
=== CONFIGURAÇÃO DE PERMISSÕES ANDROID ===

1. Adicione as permissões no android/app/src/main/AndroidManifest.xml:

$manifestContent

2. As permissões são solicitadas automaticamente pelos services:
   - LocalizacaoBackgroundService: solicita permissões de localização
   - NotificacaoForegroundService: gerencia notificação persistente
   - GerenciadorServicosBackground: coordena ambos os serviços

3. Uso no código:

```dart
// No HomeController
await _iniciarServicosBackground();

// Verifica se os serviços estão ativos
final ativos = _localizacaoService._gerenciadorBackground.servicosAtivos;

// Para posição em background
final posicao = _localizacaoService.posicaoAtualBackground;
```

4. Comportamento por versão Android:
   - Android 6+ (API 23): Solicita permissões de localização
   - Android 10+ (API 29): Requer permissão de background explícita
   - Android 11+ (API 30): Permissão background deve ser solicitada separadamente

5. Importante:
   - O serviço roda em foreground para evitar limitações
   - Notificação persistente mantém o app ativo
   - Localização é atualizada mesmo com app em background
   - Filtro de distância evita atualizações desnecessárias
''';
  }
}
