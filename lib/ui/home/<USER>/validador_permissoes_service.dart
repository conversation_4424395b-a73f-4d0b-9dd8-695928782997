import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';

class ValidadorPermissoesService extends ChangeNotifier {
  LocationPermission? _ultimaPermissao;
  bool _servicoLocalizacaoHabilitado = false;

  LocationPermission? get ultimaPermissao => _ultimaPermissao;
  bool get servicoLocalizacaoHabilitado => _servicoLocalizacaoHabilitado;
  bool get permissoesCompletas => _verificarPermissoesCompletas();

  Future<bool> validarTodasPermissoes() async {
    await _verificarServicoLocalizacao();
    await _verificarPermissoesLocalizacao();

    notifyListeners();
    return permissoesCompletas;
  }

  Future<void> _verificarServicoLocalizacao() async {
    _servicoLocalizacaoHabilitado = await Geolocator.isLocationServiceEnabled();
  }

  Future<void> _verificarPermissoesLocalizacao() async {
    _ultimaPermissao = await Geolocator.checkPermission();
  }

  bool _verificarPermissoesCompletas() {
    if (!_servicoLocalizacaoHabilitado) return false;

    return _ultimaPermissao == LocationPermission.whileInUse ||
        _ultimaPermissao == LocationPermission.always;
  }

  Future<bool> solicitarPermissoes() async {
    if (!await _solicitarServicoLocalizacao()) return false;

    return await _solicitarPermissoesLocalizacao();
  }

  Future<bool> _solicitarServicoLocalizacao() async {
    final habilitado = await Geolocator.isLocationServiceEnabled();
    if (!habilitado) {
      await Geolocator.openLocationSettings();
      return await Geolocator.isLocationServiceEnabled();
    }
    return true;
  }

  Future<bool> _solicitarPermissoesLocalizacao() async {
    LocationPermission permissao = await Geolocator.checkPermission();

    if (permissao == LocationPermission.denied) {
      permissao = await Geolocator.requestPermission();
    }

    _ultimaPermissao = permissao;
    notifyListeners();

    return _verificarPermissoesCompletas();
  }

  String obterMensagemStatus() {
    if (!_servicoLocalizacaoHabilitado) {
      return 'Serviço de localização desabilitado';
    }

    switch (_ultimaPermissao) {
      case LocationPermission.denied:
        return 'Permissão de localização negada';
      case LocationPermission.deniedForever:
        return 'Permissão negada permanentemente - abra as configurações';
      case LocationPermission.whileInUse:
        return 'Localização permitida durante uso do app';
      case LocationPermission.always:
        return 'Localização sempre permitida';
      default:
        return 'Status de permissão desconhecido';
    }
  }

  bool precisaPermissaoBackground() {
    return _ultimaPermissao == LocationPermission.whileInUse;
  }

  Future<void> abrirConfiguracoes() async {
    await Geolocator.openAppSettings();
  }
}
