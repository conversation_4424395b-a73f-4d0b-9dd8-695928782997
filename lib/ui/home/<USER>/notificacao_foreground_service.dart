import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class NotificacaoForegroundService extends ChangeNotifier {
  static const String _textoNotificacao =
      'Monitorando localização para novos pedidos';
  static const String _canalId = 'location_service_channel';
  static const int _notificationId = 1001;

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _servicoAtivo = false;
  Timer? _timerAtualizacao;

  bool get servicoAtivo => _servicoAtivo;

  Future<void> iniciarNotificacaoForeground() async {
    if (_servicoAtivo) return;

    await _inicializarPlugin();
    await _criarCanalNotificacao();
    await _exibirNotificacaoPersistente();
    _iniciarAtualizacaoPeriodica();

    _servicoAtivo = true;
    notifyListeners();
  }

  Future<void> _inicializarPlugin() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/launcher_icon');
    const initSettings = InitializationSettings(android: androidSettings);

    await _notificationsPlugin.initialize(initSettings);
  }

  Future<void> pararNotificacaoForeground() async {
    if (!_servicoAtivo) return;

    _timerAtualizacao?.cancel();
    _timerAtualizacao = null;
    await _removerNotificacao();

    _servicoAtivo = false;
    notifyListeners();
  }

  Future<void> _criarCanalNotificacao() async {
    const androidChannel = AndroidNotificationChannel(
      _canalId,
      'Serviço de Localização',
      description: 'Canal para notificações do serviço de localização',
      importance: Importance.low,
      enableVibration: false,
      playSound: false,
    );

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  Future<void> _exibirNotificacaoPersistente() async {
    const androidDetails = AndroidNotificationDetails(
      _canalId,
      'Serviço de Localização',
      channelDescription: 'Monitorando localização em segundo plano',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      enableVibration: false,
      playSound: false,
      icon: '@mipmap/launcher_icon',
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notificationsPlugin.show(
      _notificationId,
      'Tá Liso - Entregador',
      _textoNotificacao,
      notificationDetails,
    );
  }

  void _iniciarAtualizacaoPeriodica() {
    _timerAtualizacao = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _atualizarNotificacao(),
    );
  }

  Future<void> _atualizarNotificacao() async {
    if (!_servicoAtivo) return;

    final tempoAtual = DateTime.now();
    final textoAtualizado = _obterTextoComTempo(tempoAtual);

    await _atualizarConteudoNotificacao(textoAtualizado);
  }

  String _obterTextoComTempo(DateTime tempo) {
    final formatoTempo = '${tempo.hour.toString().padLeft(2, '0')}:'
        '${tempo.minute.toString().padLeft(2, '0')}';

    return '$_textoNotificacao - Ativo desde $formatoTempo';
  }

  Future<void> _atualizarConteudoNotificacao(String novoTexto) async {
    const androidDetails = AndroidNotificationDetails(
      _canalId,
      'Serviço de Localização',
      channelDescription: 'Monitorando localização em segundo plano',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      enableVibration: false,
      playSound: false,
      icon: '@mipmap/launcher_icon',
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notificationsPlugin.show(
      _notificationId,
      'Tá Liso - Entregador',
      novoTexto,
      notificationDetails,
    );
  }

  Future<void> _removerNotificacao() async {
    await _notificationsPlugin.cancel(_notificationId);
  }

  @override
  void dispose() {
    pararNotificacaoForeground();
    super.dispose();
  }
}
