import 'dart:async';

import 'package:flutter/foundation.dart';

class NotificacaoForegroundService extends ChangeNotifier {
  static const String _textoNotificacao =
      'Monitorando localização para novos pedidos';

  bool _servicoAtivo = false;
  Timer? _timerAtualizacao;

  bool get servicoAtivo => _servicoAtivo;

  Future<void> iniciarNotificacaoForeground() async {
    if (_servicoAtivo) return;

    await _criarCanalNotificacao();
    await _exibirNotificacaoPersistente();
    _iniciarAtualizacaoPeriodica();

    _servicoAtivo = true;
    notifyListeners();
  }

  Future<void> pararNotificacaoForeground() async {
    if (!_servicoAtivo) return;

    _timerAtualizacao?.cancel();
    _timerAtualizacao = null;
    await _removerNotificacao();

    _servicoAtivo = false;
    notifyListeners();
  }

  Future<void> _criarCanalNotificacao() async {
    // Implementação específica da plataforma seria necessária aqui
    // usando flutter_local_notifications ou similar
  }

  Future<void> _exibirNotificacaoPersistente() async {
    // Implementação da notificação persistente
    // que mantém o serviço em foreground
  }

  void _iniciarAtualizacaoPeriodica() {
    _timerAtualizacao = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _atualizarNotificacao(),
    );
  }

  Future<void> _atualizarNotificacao() async {
    if (!_servicoAtivo) return;

    final tempoAtual = DateTime.now();
    final textoAtualizado = _obterTextoComTempo(tempoAtual);

    await _atualizarConteudoNotificacao(textoAtualizado);
  }

  String _obterTextoComTempo(DateTime tempo) {
    final formatoTempo = '${tempo.hour.toString().padLeft(2, '0')}:'
        '${tempo.minute.toString().padLeft(2, '0')}';

    return '$_textoNotificacao - Ativo desde $formatoTempo';
  }

  Future<void> _atualizarConteudoNotificacao(String novoTexto) async {
    // Atualizar o conteúdo da notificação sem removê-la
  }

  Future<void> _removerNotificacao() async {
    // Remover a notificação do sistema
  }

  @override
  void dispose() {
    pararNotificacaoForeground();
    super.dispose();
  }
}
