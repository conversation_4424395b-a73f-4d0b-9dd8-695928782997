import 'dart:async';

import 'package:emartdriver/ui/home/<USER>/gerenciador_servicos_background.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocalizacaoService {
  final GerenciadorServicosBackground _gerenciadorBackground =
      GerenciadorServicosBackground();
  StreamSubscription<Position>? _subscricaoAtualizacoesPosicao;
  Function(LatLng)? _callbackBackground;

  Future<LatLng?> obterPosicaoAtual() async {
    if (!await _verificarPermissoes()) {
      return null;
    }

    final posicao = await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
      ),
    );

    return LatLng(posicao.latitude, posicao.longitude);
  }

  Future<bool> iniciarMonitoramentoBackground() async {
    return await _gerenciadorBackground.iniciarServicos();
  }

  Future<void> pararMonitoramentoBackground() async {
    await _gerenciadorBackground.pararServicos();
  }

  LatLng? get posicaoAtualBackground => _gerenciadorBackground.posicaoAtual;

  void iniciarMonitoramentoPosicao(Function(LatLng) aoAtualizarPosicao) {
    _subscricaoAtualizacoesPosicao?.cancel();

    const configuracaoLocalizacao = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _subscricaoAtualizacoesPosicao = Geolocator.getPositionStream(
      locationSettings: configuracaoLocalizacao,
    ).listen((Position posicao) {
      final novaLocalizacao = LatLng(posicao.latitude, posicao.longitude);
      aoAtualizarPosicao(novaLocalizacao);
    });
  }

  void pararMonitoramentoPosicao() {
    _subscricaoAtualizacoesPosicao?.cancel();
    _subscricaoAtualizacoesPosicao = null;
  }

  void configurarCallbackBackground(Function(LatLng) callback) {
    _callbackBackground = callback;
    _gerenciadorBackground.addListener(_notificarMudancaBackground);
  }

  void _notificarMudancaBackground() {
    final posicao = _gerenciadorBackground.posicaoAtual;
    if (posicao != null && _callbackBackground != null) {
      _callbackBackground!(posicao);
    }
  }

  Future<bool> _verificarPermissoes() async {
    final servicoHabilitado = await Geolocator.isLocationServiceEnabled();
    if (!servicoHabilitado) {
      return false;
    }

    LocationPermission permissao = await Geolocator.checkPermission();
    if (permissao == LocationPermission.denied) {
      permissao = await Geolocator.requestPermission();
      if (permissao == LocationPermission.deniedForever) {
        return false;
      }
    }

    return permissao != LocationPermission.denied;
  }

  void dispose() {
    pararMonitoramentoPosicao();
    _gerenciadorBackground.dispose();
  }
}
