import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/vendor_status_enum.dart';

class FirebaseOrderService {
  static const int _limitePedidos = 1;

  Future<QuerySnapshot> buscarPedidosDisponiveis() async {
    return await FirebaseFirestore.instance
        .collection(ORDERS)
        .where('status', isEqualTo: OrderStatus.driverSearching.description)
        .get();
  }

  Future<QuerySnapshot> buscarPedidosAceitos(String entregadorId) async {
    return await FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: entregadorId)
        .where('status', whereIn: [
          OrderStatus.driverAccepted.description,
          OrderStatus.driverOnTheWay.description,
          OrderStatus.driverPending.description
        ])
        .limit(_limitePedidos)
        .get();
  }

  Future<QuerySnapshot> buscarPedidosDevolucao(String entregadorId) async {
    return await FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: entregadorId)
        .where('status', isEqualTo: OrderStatus.delivered.description)
        .where('has_return', isEqualTo: true)
        .limit(_limitePedidos)
        .get();
  }

  Future<QuerySnapshot> buscarDevolucoesPendentes(String entregadorId) async {
    return await FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: entregadorId)
        .where('has_return', isEqualTo: true)
        .where('status', isEqualTo: OrderStatus.returned.description)
        .limit(_limitePedidos)
        .get();
  }

  Stream<QuerySnapshot> monitorarPedidosDisponiveis() {
    return FirebaseFirestore.instance
        .collection(ORDERS)
        .where('status', isEqualTo: OrderStatus.driverSearching.description)
        .snapshots();
  }

  Stream<DocumentSnapshot> monitorarPedidoEspecifico(String pedidoId) {
    return FirebaseFirestore.instance
        .collection(ORDERS)
        .doc(pedidoId)
        .snapshots();
  }

  Stream<QuerySnapshot> monitorarDevolucoesPendentes(String entregadorId) {
    return FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: entregadorId)
        .where('has_return', isEqualTo: true)
        .snapshots();
  }

  Future<void> aceitarPedido(String pedidoId, String entregadorId) async {
    final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(pedidoId);

    await docRef.update({
      "entregador_id": entregadorId,
      "horaAceite": Timestamp.now(),
      "status": OrderStatus.driverAccepted.description,
    });
  }

  Future<void> confirmarDevolucao(String pedidoId) async {
    final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(pedidoId);

    await docRef.update({
      'status': OrderStatus.returned.description,
    });
  }

  Future<DocumentSnapshot> obterPedido(String pedidoId) async {
    return await FirebaseFirestore.instance
        .collection(ORDERS)
        .doc(pedidoId)
        .get();
  }

  Future<bool> verificarDevolucaoPendente(String entregadorId) async {
    try {
      final snapshot = await buscarDevolucoesPendentes(entregadorId);
      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log("Erro ao verificar devolução pendente: $e");
      return false;
    }
  }
}
