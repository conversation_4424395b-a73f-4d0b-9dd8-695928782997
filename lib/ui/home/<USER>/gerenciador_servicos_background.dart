import 'package:emartdriver/ui/home/<USER>/localizacao_background_service.dart';
import 'package:emartdriver/ui/home/<USER>/notificacao_foreground_service.dart';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class GerenciadorServicosBackground extends ChangeNotifier {
  final LocalizacaoBackgroundService _servicoLocalizacao =
      LocalizacaoBackgroundService();
  final NotificacaoForegroundService _servicoNotificacao =
      NotificacaoForegroundService();

  bool _servicosAtivos = false;

  bool get servicosAtivos => _servicosAtivos;
  LatLng? get posicaoAtual => _servicoLocalizacao.posicaoAtual;

  Future<bool> iniciarServicos() async {
    if (_servicosAtivos) return true;

    final localizacaoIniciada = await _servicoLocalizacao.iniciarServico();
    if (!localizacaoIniciada) return false;

    await _servicoNotificacao.iniciarNotificacaoForeground();

    _configurarListeners();
    _servicosAtivos = true;
    notifyListeners();

    return true;
  }

  Future<void> pararServicos() async {
    if (!_servicosAtivos) return;

    await _servicoLocalizacao.pararServico();
    await _servicoNotificacao.pararNotificacaoForeground();
    _removerListeners();

    _servicosAtivos = false;
    notifyListeners();
  }

  void _configurarListeners() {
    _servicoLocalizacao.addListener(_notificarMudancaLocalizacao);
  }

  void _removerListeners() {
    _servicoLocalizacao.removeListener(_notificarMudancaLocalizacao);
  }

  void _notificarMudancaLocalizacao() {
    notifyListeners();
  }

  Future<LatLng?> obterPosicaoAtual() async {
    return await _servicoLocalizacao.obterPosicaoUnicaVez();
  }

  bool verificarStatusPermissoes() {
    return _servicoLocalizacao.servicoAtivo;
  }

  @override
  void dispose() {
    pararServicos();
    _servicoLocalizacao.dispose();
    _servicoNotificacao.dispose();
    super.dispose();
  }
}
