import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocalizacaoBackgroundService extends ChangeNotifier {
  static const int _intervaloAtualizacaoSegundos = 10;
  static const int _distanciaFiltroMetros = 5;

  LatLng? _posicaoAtual;
  bool _servicoAtivo = false;
  StreamSubscription<Position>? _subscricaoLocalizacao;

  LatLng? get posicaoAtual => _posicaoAtual;
  bool get servicoAtivo => _servicoAtivo;

  Future<bool> iniciarServico() async {
    if (_servicoAtivo) return true;

    final permissoesObtidas = await _solicitarPermissoes();
    if (!permissoesObtidas) return false;

    await _configurarMonitoramentoBackground();
    _servicoAtivo = true;
    notifyListeners();

    return true;
  }

  Future<void> pararServico() async {
    if (!_servicoAtivo) return;

    await _subscricaoLocalizacao?.cancel();
    _subscricaoLocalizacao = null;
    _servicoAtivo = false;
    notifyListeners();
  }

  Future<bool> _solicitarPermissoes() async {
    final servicoHabilitado = await _verificarServicoLocalizacao();
    if (!servicoHabilitado) return false;

    final permissaoBasica = await _obterPermissaoBasica();
    if (!permissaoBasica) return false;

    return await _obterPermissaoBackground();
  }

  Future<bool> _verificarServicoLocalizacao() async {
    final habilitado = await Geolocator.isLocationServiceEnabled();
    return habilitado;
  }

  Future<bool> _obterPermissaoBasica() async {
    LocationPermission permissao = await Geolocator.checkPermission();

    if (permissao == LocationPermission.denied) {
      permissao = await Geolocator.requestPermission();
    }

    return _permissaoValida(permissao);
  }

  Future<bool> _obterPermissaoBackground() async {
    try {
      final permissao = await Geolocator.checkPermission();
      if (permissao == LocationPermission.whileInUse) {
        return true;
      }

      return permissao == LocationPermission.always;
    } catch (e) {
      return false;
    }
  }

  bool _permissaoValida(LocationPermission permissao) {
    return permissao == LocationPermission.whileInUse ||
        permissao == LocationPermission.always;
  }

  Future<void> _configurarMonitoramentoBackground() async {
    final configuracao = _criarConfiguracaoLocalizacao();

    _subscricaoLocalizacao = Geolocator.getPositionStream(
      locationSettings: configuracao,
    ).listen(_processarNovaPosicao);
  }

  LocationSettings _criarConfiguracaoLocalizacao() {
    return const LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: _distanciaFiltroMetros,
      timeLimit: Duration(seconds: _intervaloAtualizacaoSegundos),
    );
  }

  void _processarNovaPosicao(Position posicao) {
    final novaPosicao = LatLng(posicao.latitude, posicao.longitude);

    if (_posicaoMudouSignificativamente(novaPosicao)) {
      _posicaoAtual = novaPosicao;
      notifyListeners();
    }
  }

  bool _posicaoMudouSignificativamente(LatLng novaPosicao) {
    if (_posicaoAtual == null) return true;

    final distancia = Geolocator.distanceBetween(
      _posicaoAtual!.latitude,
      _posicaoAtual!.longitude,
      novaPosicao.latitude,
      novaPosicao.longitude,
    );

    return distancia >= _distanciaFiltroMetros;
  }

  Future<LatLng?> obterPosicaoUnicaVez() async {
    try {
      final permissoesObtidas = await _solicitarPermissoes();
      if (!permissoesObtidas) return null;

      final posicao = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      return LatLng(posicao.latitude, posicao.longitude);
    } catch (e) {
      return null;
    }
  }

  @override
  void dispose() {
    pararServico();
    super.dispose();
  }
}
