import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>/firebase_order_service.dart';

class MonitoramentoPedidosService {
  final FirebaseOrderService _firebaseService = FirebaseOrderService();

  StreamSubscription<QuerySnapshot>? _subscricaoPedidosDisponiveis;
  StreamSubscription<DocumentSnapshot>? _subscricaoPedidoAceito;
  StreamSubscription<QuerySnapshot>? _subscricaoDevolucoesPendentes;

  void iniciarMonitoramentoPedidosDisponiveis({
    required Function(List<OrderModel>) aoReceberPedidos,
  }) {
    _subscricaoPedidosDisponiveis?.cancel();

    _subscricaoPedidosDisponiveis = _firebaseService
        .monitorarPedidosDisponiveis()
        .listen((QuerySnapshot snapshot) {
      try {
        final pedidos = snapshot.docs
            .map((doc) =>
                OrderModel.fromJson(doc.data() as Map<String, dynamic>))
            .toList();

        aoReceberPedidos(pedidos);
      } catch (e) {
        log("Erro ao processar pedidos disponíveis: $e");
      }
    }, onError: (e) {
      log("Erro no monitoramento de pedidos disponíveis: $e");
    });
  }

  void iniciarMonitoramentoPedidoAceito({
    required String pedidoId,
    required Function(OrderModel) aoAtualizarPedido,
    required Function() aoPedidoNaoEncontrado,
  }) {
    _subscricaoPedidoAceito?.cancel();

    _subscricaoPedidoAceito = _firebaseService
        .monitorarPedidoEspecifico(pedidoId)
        .listen((DocumentSnapshot snapshot) {
      try {
        if (snapshot.exists) {
          final pedido =
              OrderModel.fromJson(snapshot.data() as Map<String, dynamic>);
          aoAtualizarPedido(pedido);
        } else {
          aoPedidoNaoEncontrado();
        }
      } catch (e) {
        log("Erro ao processar pedido aceito: $e");
      }
    }, onError: (e) {
      log("Erro no monitoramento de pedido aceito: $e");
    });
  }

  void iniciarMonitoramentoDevolucoesPendentes({
    required String entregadorId,
    required Function(bool) aoMudarStatusDevolucao,
  }) {
    _subscricaoDevolucoesPendentes?.cancel();

    _subscricaoDevolucoesPendentes = _firebaseService
        .monitorarDevolucoesPendentes(entregadorId)
        .listen((QuerySnapshot snapshot) {
      try {
        final devolucoesPendentes = snapshot.docs.where((doc) {
          final dados = doc.data() as Map<String, dynamic>;
          final status = dados['status'] as String?;
          return status == 'Pedido devolvido';
        }).toList();

        final temDevolucaoPendente = devolucoesPendentes.isNotEmpty;
        aoMudarStatusDevolucao(temDevolucaoPendente);
      } catch (e) {
        log("Erro ao processar devoluções pendentes: $e");
      }
    }, onError: (e) {
      log("Erro no monitoramento de devoluções pendentes: $e");
    });
  }

  void pararTodosMonitoramentos() {
    _subscricaoPedidosDisponiveis?.cancel();
    _subscricaoPedidoAceito?.cancel();
    _subscricaoDevolucoesPendentes?.cancel();
  }

  void dispose() {
    pararTodosMonitoramentos();
  }
}
