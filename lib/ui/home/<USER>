import 'package:emartdriver/ui/home/<USER>/botoes_controle_mapa.dart';
import 'package:emartdriver/ui/home/<USER>/bottomsheet_devolucao_pendente.dart';
import 'package:emartdriver/ui/home/<USER>/home_controller.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/firebase_order_service.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  late final HomeController _controller;
  final FirebaseOrderService _firebaseService = FirebaseOrderService();

  @override
  void initState() {
    super.initState();
    _controller = HomeController();

    _controller.inicializar();

    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _verificarDevolucaoPendente();
      _iniciarMonitoramentoDevolucao();
    });
  }

  @override
  void didUpdateWidget(HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      _controller.recarregarMapa();
      _verificarDevolucaoPendente();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (didPop) return;
        await _verificarSaidaApp();
      },
      child: Scaffold(
        body: ListenableBuilder(
          listenable: _controller.homeState,
          builder: (context, _) {
            if (_controller.homeState.carregando) {
              return const Center(child: CircularProgressIndicator());
            }

            return _buildConteudoMapa();
          },
        ),
      ),
    );
  }

  Widget _buildConteudoMapa() {
    return Stack(
      children: [
        _buildMapa(),
        _buildBotoesControle(),
        _buildBarraPedidoPersistente(),
      ],
    );
  }

  Widget _buildMapa() {
    return ListenableBuilder(
      listenable: _controller.mapaState,
      builder: (context, _) {
        final posicaoInicial =
            _controller.homeState.posicaoAtual ?? const LatLng(0, 0);

        return GoogleMap(
          mapType: MapType.terrain,
          onMapCreated: _controller.aoMapaCriado,
          myLocationEnabled: false,
          myLocationButtonEnabled: false,
          zoomControlsEnabled: false,
          markers: _controller.mapaState.marcadores.values.toSet(),
          polylines: _controller.mapaState.polylines,
          initialCameraPosition: CameraPosition(
            target: posicaoInicial,
            zoom: 17,
          ),
          onTap: _controller.aoClicarMapa,
        );
      },
    );
  }

  Widget _buildBotoesControle() {
    return BotoesControleMapa(
      aoZoomIn: _controller.aumentarZoom,
      aoZoomOut: _controller.diminuirZoom,
      aoCentralizarLocalizacao: _controller.centralizarNoEntregador,
      aoRecarregarMapa: _controller.recarregarMapa,
    );
  }

  Widget _buildBarraPedidoPersistente() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: ListenableBuilder(
        listenable: _controller.homeState,
        builder: (context, _) {
          return PersistentOrderBar(
            pedidoAceito: _controller.homeState.pedidoAceito,
            selectedOrder: _controller.homeState.pedidoSelecionado,
            distanceKm: _controller.homeState.informacaoRota != null
                ? (_controller.homeState.informacaoRota!.distance) / 1000
                : null,
          );
        },
      ),
    );
  }

  Future<void> _verificarSaidaApp() async {
    final temDevolucaoPendente =
        await _firebaseService.verificarDevolucaoPendente(
      _controller.entregadorId,
    );

    if (temDevolucaoPendente) {
      if (mounted) {
        _exibirMensagemDevolucaoPendente();
        _mostrarBottomsheetDevolucaoPendente();
      }
    } else {
      Navigator.of(context).pop();
    }
  }

  void _exibirMensagemDevolucaoPendente() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Você não pode sair do aplicativo até que a devolução seja confirmada pelo lojista',
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _mostrarBottomsheetDevolucaoPendente() {
    if (_controller.homeState.bottomsheetDevolucaoAtivo) return;

    _controller.homeState.alterarBottomsheetDevolucao(true);

    showModalBottomSheet(
      enableDrag: false,
      isDismissible: false,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return const BottomsheetDevolucaoPendente();
      },
    ).then((_) {
      _controller.homeState.alterarBottomsheetDevolucao(false);
    });
  }

  Future<void> _verificarDevolucaoPendente() async {
    if (!mounted || _controller.homeState.bottomsheetDevolucaoAtivo) return;

    final temDevolucaoPendente =
        await _firebaseService.verificarDevolucaoPendente(
      _controller.entregadorId,
    );

    if (temDevolucaoPendente) {
      _mostrarBottomsheetDevolucaoPendente();
    }
  }

  void _iniciarMonitoramentoDevolucao() {
    // Aqui seria implementado o monitoramento em tempo real das devoluções
    // usando o MonitoramentoPedidosService
  }
}
