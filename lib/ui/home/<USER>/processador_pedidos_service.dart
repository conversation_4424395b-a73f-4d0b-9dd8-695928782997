import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class ProcessadorPedidosService {
  Map<String, List<OrderModel>> agruparPedidosPorLoja(
    List<OrderModel> pedidos,
    LatLng posicaoEntregador,
  ) {
    final Map<String, List<OrderModel>> pedidosPorLoja = {};

    for (final pedido in pedidos) {
      final posicaoLoja = _extrairPosicaoLoja(pedido);

      if (posicaoLoja == null) continue;

      if (_estaForaDoAlcance(posicaoEntregador, posicaoLoja)) {
        continue;
      }

      final lojaId = pedido.vendor.id;
      pedidosPorLoja.putIfAbsent(lojaId, () => []).add(pedido);
    }

    return pedidosPorLoja;
  }

  LatLng? _extrairPosicaoLoja(OrderModel pedido) {
    final geoPoint = pedido.vendor.address_store?.location.geoPoint;
    if (geoPoint == null) return null;

    return LatLng(geoPoint.latitude, geoPoint.longitude);
  }

  bool _estaForaDoAlcance(LatLng posicaoEntregador, LatLng posicaoLoja) {
    final distanciaKm = calculateDistance(
      posicaoEntregador.latitude,
      posicaoEntregador.longitude,
      posicaoLoja.latitude,
      posicaoLoja.longitude,
    );

    return distanciaKm > maxStoreDistanceInKM;
  }
}
