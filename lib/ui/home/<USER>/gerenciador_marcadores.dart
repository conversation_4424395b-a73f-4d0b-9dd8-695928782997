import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:emartdriver/model/OrderModel.dart';

class GerenciadorMarcadores {
  static Marker criarMarcadorEntregador({
    required LatLng posicao,
    required BitmapDescriptor icone,
  }) {
    return Marker(
      markerId: const MarkerId('entregador'),
      position: posicao,
      icon: icone,
      rotation: posicao.latitude,
    );
  }

  static Marker criarMarcadorLoja({
    required String lojaId,
    required LatLng posicao,
    required String titulo,
    required bool multiplosPedidos,
    required VoidCallback aoClicar,
  }) {
    return Marker(
      markerId: MarkerId('loja_$lojaId'),
      position: posicao,
      icon: BitmapDescriptor.defaultMarkerWithHue(
        multiplosPedidos
            ? BitmapDescriptor.hueOrange
            : BitmapDescriptor.hueGreen,
      ),
      infoWindow: InfoWindow(title: titulo, snippet: ""),
      onTap: aoClicar,
    );
  }

  static Marker criarMarcadorDestino({
    required LatLng posicao,
    required String titulo,
    required String snippet,
    required bool eEntrega,
    required VoidCallback aoClicar,
  }) {
    return Marker(
      markerId: const MarkerId('destino'),
      position: posicao,
      icon: BitmapDescriptor.defaultMarkerWithHue(
        eEntrega ? BitmapDescriptor.hueRed : BitmapDescriptor.hueGreen,
      ),
      infoWindow: InfoWindow(title: titulo, snippet: snippet),
      onTap: aoClicar,
    );
  }

  static Marker criarMarcadorDevolucao({
    required LatLng posicao,
    required String titulo,
    required VoidCallback aoClicar,
  }) {
    return Marker(
      markerId: const MarkerId('destino'),
      position: posicao,
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(title: titulo, snippet: ""),
      onTap: aoClicar,
    );
  }

  static Polyline criarPolyline({
    required String id,
    required List<LatLng> pontos,
    required Color cor,
    int largura = 5,
  }) {
    return Polyline(
      polylineId: PolylineId(id),
      points: pontos,
      color: cor,
      width: largura,
    );
  }

  static LatLng? extrairPosicaoLoja(OrderModel pedido) {
    final geoPoint = pedido.vendor.address_store?.location.geoPoint;
    if (geoPoint == null) return null;

    return LatLng(geoPoint.latitude, geoPoint.longitude);
  }

  static LatLng? extrairPosicaoCliente(OrderModel pedido) {
    try {
      final endereco = pedido.author.shippingAddress?.firstWhere(
          (a) => a.isDefault == true,
          orElse: () => pedido.author.shippingAddress!.first);

      final geoPoint = endereco?.location?.geoPoint;
      if (geoPoint == null) return null;

      return LatLng(geoPoint.latitude, geoPoint.longitude);
    } catch (e) {
      log("Erro ao extrair posição do cliente: $e");
      return null;
    }
  }

  static String obterTituloMarcadorLoja(
    String nomeLoja,
    int quantidadePedidos,
  ) {
    return quantidadePedidos == 1
        ? "Loja: $nomeLoja"
        : "Loja: $nomeLoja ($quantidadePedidos pedidos)";
  }
}
