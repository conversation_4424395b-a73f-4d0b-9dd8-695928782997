import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class IconeService {
  static const String _caminhoIconeEntregador =
      'assets/images/motoentregador.png';
  static const int _tamanhoIcone = 40;

  Future<BitmapDescriptor> carregarIconeEntregador() async {
    try {
      final Uint8List bytesIcone = await _obterBytesDoAsset(
        _caminhoIconeEntregador,
        _tamanhoIcone,
      );

      return BitmapDescriptor.bytes(bytesIcone);
    } catch (e) {
      log("Erro ao carregar ícone do entregador: $e");
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  Future<Uint8List> _obterBytesDoAsset(String caminho, int largura) async {
    ByteData dados = await rootBundle.load(caminho);
    ui.Codec codec = await ui.instantiateImageCodec(
      dados.buffer.asUint8List(),
      targetWidth: largura,
    );
    ui.FrameInfo frameInfo = await codec.getNextFrame();

    return (await frameInfo.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }
}
