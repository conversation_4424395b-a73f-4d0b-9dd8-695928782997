import 'package:emartdriver/ui/home/<USER>/validador_permissoes_service.dart';
import 'package:flutter/material.dart';

class StatusPermissoesWidget extends StatefulWidget {
  final VoidCallback? aoPermissoesObtidas;

  const StatusPermissoesWidget({
    super.key,
    this.aoPermissoesObtidas,
  });

  @override
  State<StatusPermissoesWidget> createState() => _StatusPermissoesWidgetState();
}

class _StatusPermissoesWidgetState extends State<StatusPermissoesWidget> {
  final ValidadorPermissoesService _validador = ValidadorPermissoesService();
  bool _verificando = false;

  @override
  void initState() {
    super.initState();
    _verificarPermissoes();
    _validador.addListener(_aoMudarPermissoes);
  }

  @override
  void dispose() {
    _validador.removeListener(_aoMudarPermissoes);
    _validador.dispose();
    super.dispose();
  }

  void _aoMudarPermissoes() {
    if (mounted) {
      setState(() {});

      if (_validador.permissoesCompletas &&
          widget.aoPermissoesObtidas != null) {
        widget.aoPermissoesObtidas!();
      }
    }
  }

  Future<void> _verificarPermissoes() async {
    setState(() => _verificando = true);
    await _validador.validarTodasPermissoes();
    setState(() => _verificando = false);
  }

  Future<void> _solicitarPermissoes() async {
    setState(() => _verificando = true);
    await _validador.solicitarPermissoes();
    setState(() => _verificando = false);
  }

  @override
  Widget build(BuildContext context) {
    if (_verificando) {
      return _buildIndicadorCarregamento();
    }

    if (_validador.permissoesCompletas) {
      return _buildStatusSucesso();
    }

    return _buildSolicitacaoPermissoes();
  }

  Widget _buildIndicadorCarregamento() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Verificando permissões...'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSucesso() {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green[600]),
            const SizedBox(width: 16),
            const Expanded(
              child: Text('Permissões configuradas corretamente'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolicitacaoPermissoes() {
    return Card(
      color: Colors.orange[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.orange[600]),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Permissões necessárias',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(_validador.obterMensagemStatus()),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _solicitarPermissoes,
                  child: const Text('Permitir Localização'),
                ),
                if (_validador.ultimaPermissao != null) ...[
                  const SizedBox(width: 12),
                  TextButton(
                    onPressed: _validador.abrirConfiguracoes,
                    child: const Text('Configurações'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
